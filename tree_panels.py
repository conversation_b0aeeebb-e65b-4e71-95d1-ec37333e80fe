import bpy
from bpy.types import Panel
from .panel_backgrounds import SDFPanelMixin

class SDF_PT_TreePanel(Panel, SDFPanelMixin):
    """Main SDF Tree Panel"""
    bl_label = "Arcane SDF - Tree Builder"
    bl_idname = "SDF_PT_tree_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Arcane SDF"
    
    def draw(self, context):
        try:
            # Draw arcane.png background
            self.draw_arcane_background(context)

            layout = self.layout
            scene = context.scene
            
            # Main header
            col = layout.column()
            col.label(text="SDF Tree Builder", icon='OUTLINER')
            
            # Viewport controls
            viewport_box = layout.box()
            viewport_box.label(text="Viewport", icon='VIEW3D')

            # Viewport widgets toggle
            row = viewport_box.row()
            row.operator("sdf.toggle_viewport_widgets", text="Toggle Transform Widgets", icon='GIZMO')
            
            if hasattr(scene, 'sdf'):
                viewport_box.prop(scene.sdf, "sdf_show_in_viewport", text="Show in Viewport")
                if scene.sdf.sdf_show_in_viewport:
                    viewport_box.prop(scene.sdf, "sdf_viewport_quality", slider=True)

                    # Material/Shading controls
                    if hasattr(scene.sdf, 'material'):
                        material_box = layout.box()
                        material_box.label(text="Material & Shading", icon='MATERIAL')

                        mat = scene.sdf.material
                        material_box.prop(mat, "base_color")
                        material_box.prop(mat, "shading_mode")

                        if mat.shading_mode in ['1', '2']:  # Matcap or Mixed
                            material_box.prop(mat, "use_matcap")
                            if mat.use_matcap:
                                material_box.prop(mat, "matcap_intensity", slider=True)

                                # Matcap selection
                                matcap_box = material_box.box()
                                matcap_box.label(text="Matcap Selection", icon='SHADING_TEXTURE')

                                # Blender's built-in matcaps
                                matcap_box.prop(mat, "matcap_studio_light", text="Studio Light")

                                # Custom matcap override
                                matcap_box.prop(mat, "matcap_image", text="Custom Image")

                                # Matcap loading buttons
                                row = matcap_box.row()
                                row.operator("sdf.load_default_matcap", text="Default")
                                row.operator("sdf.load_matcap_file", text="File")
                                row = matcap_box.row()
                                row.operator("sdf.load_matcap_from_blender", text="From Blender Image")
                                row.operator("sdf.load_viewport_matcap", text="From Viewport")
            
            layout.separator()
            
            # Quick Add Primitives
            primitives_box = layout.box()
            primitives_box.label(text="Add Primitives", icon='MESH_UVSPHERE')
            
            # Primitive buttons in a grid
            grid = primitives_box.grid_flow(columns=2, even_columns=True)
            grid.operator("sdf.tree_add_sphere", text="Sphere", icon='MESH_UVSPHERE')
            grid.operator("sdf.tree_add_box", text="Box", icon='MESH_CUBE')
            grid.operator("sdf.tree_add_cylinder", text="Cylinder", icon='MESH_CYLINDER')
            grid.operator("sdf.tree_add_torus", text="Torus", icon='MESH_TORUS')

            # Group and nesting controls (only show if tree exists)
            if hasattr(scene, 'sdf_tree'):
                tree = scene.sdf_tree

                layout.separator()
                nesting_box = layout.box()
                nesting_box.label(text="Grouping & Nesting", icon='OUTLINER_COLLECTION')

                row = nesting_box.row(align=True)
                row.operator("sdf.tree_add_group", text="Add Group", icon='OUTLINER_COLLECTION')

                if tree.items and 0 <= tree.active_index < len(tree.items):
                    row = nesting_box.row(align=True)
                    row.operator("sdf.tree_make_child", text="Make Child Of...", icon='CONSTRAINT')

                    # Quick add as child buttons
                    nesting_box.label(text="Add as Child:")
                    child_grid = nesting_box.grid_flow(columns=2, even_columns=True)

                    add_child_sphere = child_grid.operator("sdf.tree_add_as_child", text="Sphere", icon='MESH_UVSPHERE')
                    add_child_sphere.item_type = 'SPHERE'

                    add_child_box = child_grid.operator("sdf.tree_add_as_child", text="Box", icon='MESH_CUBE')
                    add_child_box.item_type = 'BOX'

                    add_child_cylinder = child_grid.operator("sdf.tree_add_as_child", text="Cylinder", icon='MESH_CYLINDER')
                    add_child_cylinder.item_type = 'CYLINDER'

                    add_child_group = child_grid.operator("sdf.tree_add_as_child", text="Group", icon='OUTLINER_COLLECTION')
                    add_child_group.item_type = 'GROUP'
            
            # Info box about boolean operations
            info_box = layout.box()
            info_box.label(text="Boolean Operations", icon='INFO')
            info_box.label(text="Select a primitive and change its", icon='BLANK1')
            info_box.label(text="'Boolean Mode' in the properties below", icon='BLANK1')
            
            layout.separator()
            
            # SDF Tree List
            tree_box = layout.box()
            tree_box.label(text="SDF Tree", icon='OUTLINER_OB_GROUP_INSTANCE')

            if hasattr(scene, 'sdf_tree'):
                tree = scene.sdf_tree  # Re-assign for clarity, already defined above

                # Tree list
                row = tree_box.row()
                row.template_list("SDF_UL_TreeList", "", tree, "items", tree, "active_index", rows=6)

                # Tree controls
                col = row.column(align=True)
                col.operator("sdf.tree_add_item", text="", icon='ADD')
                col.operator("sdf.tree_remove_item", text="", icon='REMOVE')
                col.separator()
                col.operator("sdf.tree_move_item", text="", icon='TRIA_UP').direction = 'UP'
                col.operator("sdf.tree_move_item", text="", icon='TRIA_DOWN').direction = 'DOWN'
                col.separator()
                col.operator("sdf.tree_duplicate_item", text="", icon='DUPLICATE')
                col.operator("sdf.tree_clear", text="", icon='TRASH')

                # Item properties
                if tree.items and 0 <= tree.active_index < len(tree.items):
                    self.draw_item_properties(tree_box, tree.items[tree.active_index])
            else:
                tree_box.label(text="SDF Tree not initialized", icon='ERROR')
                tree_box.operator("sdf.tree_initialize", text="Initialize SDF Tree", icon='ADD')
        
        except Exception as e:
            layout.label(text=f"Panel Error: {str(e)}", icon='ERROR')
    
    def draw_item_properties(self, layout, item):
        """Draw properties for the selected item"""
        if not item:
            return
        
        layout.separator()
        
        # Item header
        header_box = layout.box()
        header_box.label(text=f"{item.item_type.title()} Properties", icon='PROPERTIES')
        
        # Basic properties
        props_box = layout.box()
        props_box.prop(item, "name")
        
        # Type-specific properties
        # Show boolean mode for all primitives and groups
        if item.item_type in ['SPHERE', 'BOX', 'CYLINDER', 'TORUS', 'GROUP']:
            props_box.prop(item, "boolean_mode")
            props_box.prop(item, "smooth_radius")
            props_box.separator()

        if item.item_type == 'SPHERE':
            props_box.prop(item, "radius")
            props_box.prop(item, "location")
            # Sphere edge modification
            edge_box = props_box.box()
            edge_box.label(text="Edge Modification", icon='MOD_BEVEL')
            edge_box.prop(item, "bevel_radius", text="Bevel (Shrink)")

        elif item.item_type == 'BOX':
            props_box.prop(item, "size")
            props_box.prop(item, "location")
            # Box edge modification
            edge_box = props_box.box()
            edge_box.label(text="Edge Modification", icon='MOD_BEVEL')
            edge_box.prop(item, "bevel_radius", text="Bevel (Rounded)")
            edge_box.prop(item, "chamfer_size", text="Chamfer (Cut)")
            edge_box.prop(item, "fillet_radius", text="Fillet (Internal)")

        elif item.item_type == 'CYLINDER':
            props_box.prop(item, "radius")
            props_box.prop(item, "height")
            props_box.prop(item, "location")
            # Cylinder edge modification
            edge_box = props_box.box()
            edge_box.label(text="Edge Modification", icon='MOD_BEVEL')
            edge_box.prop(item, "bevel_radius", text="Bevel (Rounded)")
            edge_box.prop(item, "chamfer_size", text="Chamfer (Cut)")
            edge_box.prop(item, "fillet_radius", text="Fillet (Internal)")

        elif item.item_type == 'TORUS':
            props_box.prop(item, "major_radius")
            props_box.prop(item, "minor_radius")
            props_box.prop(item, "location")
            # Torus edge modification
            edge_box = props_box.box()
            edge_box.label(text="Edge Modification", icon='MOD_BEVEL')
            edge_box.prop(item, "bevel_radius", text="Bevel (Shrink)")

        elif item.item_type == 'GROUP':
            # Group transforms
            props_box.prop(item, "location", text="Group Offset")
            props_box.prop(item, "rotation", text="Group Rotation")
            props_box.prop(item, "scale", text="Group Scale")

            # Show children count
            scene = bpy.context.scene
            if hasattr(scene, 'sdf_tree'):
                tree = scene.sdf_tree
                children = tree.get_children(tree.active_index)
                props_box.label(text=f"Children: {len(children)}", icon='OUTLINER_DATA_GP_LAYER')

        elif item.item_type == 'TRANSLATE':
            props_box.prop(item, "location", text="Offset")
        
        elif item.item_type == 'ROTATE':
            props_box.prop(item, "rotation")
        
        elif item.item_type == 'SCALE':
            props_box.prop(item, "scale")

class SDF_PT_TreeQuickActions(Panel):
    """Quick Actions Panel"""
    bl_label = "Quick Actions"
    bl_idname = "SDF_PT_tree_quick_actions"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Arcane SDF"
    bl_parent_id = "SDF_PT_tree_panel"
    
    def draw(self, context):
        try:
            layout = self.layout
            scene = context.scene
            
            if hasattr(scene, 'sdf_tree'):
                tree = scene.sdf_tree
                
                # Quick actions
                col = layout.column(align=True)
                col.operator("sdf.tree_duplicate_item", text="Duplicate Selected", icon='DUPLICATE')
                col.operator("sdf.tree_remove_item", text="Delete Selected", icon='TRASH')
                
                layout.separator()
                
                # Tree operations
                col = layout.column(align=True)
                col.operator("sdf.tree_clear", text="Clear All", icon='TRASH')
                
                # Export/Import (placeholder for future)
                layout.separator()
                col = layout.column(align=True)
                col.label(text="Export/Import (Coming Soon)")
                col.enabled = False
                # Commented out until operators are implemented
                # col.operator("sdf.tree_export", text="Export Tree", icon='EXPORT')
                # col.operator("sdf.tree_import", text="Import Tree", icon='IMPORT')
        
        except Exception as e:
            layout.label(text=f"Panel Error: {str(e)}", icon='ERROR')

class SDF_PT_TreeSettings(Panel, SDFPanelMixin):
    """Tree Settings Panel"""
    bl_label = "Render Settings"
    bl_idname = "SDF_PT_tree_settings"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Arcane SDF"
    bl_parent_id = "SDF_PT_tree_panel"
    
    def draw(self, context):
        try:
            # Draw arcane.png background
            self.draw_arcane_background(context)

            layout = self.layout
            scene = context.scene
            
            if hasattr(scene, 'sdf'):
                sdf_props = scene.sdf
                
                # Render quality settings
                col = layout.column(align=True)
                col.prop(sdf_props, "sdf_max_steps")
                col.prop(sdf_props, "sdf_surface_threshold", slider=True)
                
                layout.separator()
                
                # Update button
                layout.operator("sdf.update_viewport", text="Update Viewport", icon='FILE_REFRESH')
        
        except Exception as e:
            layout.label(text=f"Panel Error: {str(e)}", icon='ERROR')

# Register classes
classes = [
    SDF_PT_TreePanel,
    SDF_PT_TreeQuickActions,
    SDF_PT_TreeSettings,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except:
            pass
