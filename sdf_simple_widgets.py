"""
Simple SDF Viewport Widgets using draw handlers.
A more reliable approach than complex gizmos.
"""

import bpy
import gpu
from gpu_extras.batch import batch_for_shader
from mathutils import Vector
import bmesh

# Global state for widget interaction
_widget_state = {
    'enabled': False,
    'draw_handler': None,
    'mouse_handler': None,
}

def get_active_sdf_item():
    """Get the currently active SDF item and tree"""
    scene = bpy.context.scene
    if not hasattr(scene, 'sdf_tree'):
        return None, None
    
    tree = scene.sdf_tree
    if not tree.items or tree.active_index < 0 or tree.active_index >= len(tree.items):
        return None, None
    
    return tree.items[tree.active_index], tree

def draw_simple_widget():
    """Draw simple transform widget for the active SDF item"""
    item, tree = get_active_sdf_item()
    if not item:
        return

    try:
        # Get world location
        world_location = Vector(item.get_world_location(tree))
        print(f"Drawing widget at world location: {world_location} for item: {item.name}")

        # Set up GPU state
        gpu.state.blend_set('ALPHA')
        gpu.state.depth_test_set('LESS_EQUAL')
        gpu.state.line_width_set(4.0)
        
        # Create shader
        shader = gpu.shader.from_builtin('3D_UNIFORM_COLOR')
        
        # Draw coordinate axes
        axis_length = 1.5
        
        # X axis (red)
        x_verts = [
            world_location,
            world_location + Vector((axis_length, 0, 0))
        ]
        batch = batch_for_shader(shader, 'LINES', {"pos": x_verts})
        shader.bind()
        shader.uniform_float("color", (1.0, 0.2, 0.2, 0.9))
        batch.draw(shader)
        
        # Y axis (green)
        y_verts = [
            world_location,
            world_location + Vector((0, axis_length, 0))
        ]
        batch = batch_for_shader(shader, 'LINES', {"pos": y_verts})
        shader.bind()
        shader.uniform_float("color", (0.2, 1.0, 0.2, 0.9))
        batch.draw(shader)
        
        # Z axis (blue)
        z_verts = [
            world_location,
            world_location + Vector((0, 0, axis_length))
        ]
        batch = batch_for_shader(shader, 'LINES', {"pos": z_verts})
        shader.bind()
        shader.uniform_float("color", (0.2, 0.2, 1.0, 0.9))
        batch.draw(shader)
        
        # Draw center sphere (as multiple lines to form a wireframe sphere)
        sphere_radius = 0.1
        sphere_verts = []
        
        # Create simple wireframe sphere with circles
        import math
        segments = 16
        
        # XY circle
        for i in range(segments):
            angle1 = (i / segments) * 2 * math.pi
            angle2 = ((i + 1) / segments) * 2 * math.pi
            x1 = world_location.x + math.cos(angle1) * sphere_radius
            y1 = world_location.y + math.sin(angle1) * sphere_radius
            x2 = world_location.x + math.cos(angle2) * sphere_radius
            y2 = world_location.y + math.sin(angle2) * sphere_radius
            sphere_verts.extend([
                Vector((x1, y1, world_location.z)),
                Vector((x2, y2, world_location.z))
            ])
        
        # XZ circle
        for i in range(segments):
            angle1 = (i / segments) * 2 * math.pi
            angle2 = ((i + 1) / segments) * 2 * math.pi
            x1 = world_location.x + math.cos(angle1) * sphere_radius
            z1 = world_location.z + math.sin(angle1) * sphere_radius
            x2 = world_location.x + math.cos(angle2) * sphere_radius
            z2 = world_location.z + math.sin(angle2) * sphere_radius
            sphere_verts.extend([
                Vector((x1, world_location.y, z1)),
                Vector((x2, world_location.y, z2))
            ])
        
        # YZ circle
        for i in range(segments):
            angle1 = (i / segments) * 2 * math.pi
            angle2 = ((i + 1) / segments) * 2 * math.pi
            y1 = world_location.y + math.cos(angle1) * sphere_radius
            z1 = world_location.z + math.sin(angle1) * sphere_radius
            y2 = world_location.y + math.cos(angle2) * sphere_radius
            z2 = world_location.z + math.sin(angle2) * sphere_radius
            sphere_verts.extend([
                Vector((world_location.x, y1, z1)),
                Vector((world_location.x, y2, z2))
            ])
        
        # Draw sphere wireframe
        if sphere_verts:
            batch = batch_for_shader(shader, 'LINES', {"pos": sphere_verts})
            shader.bind()
            shader.uniform_float("color", (1.0, 1.0, 1.0, 0.8))
            batch.draw(shader)
        
        # Draw item name
        # (Text drawing would require more complex implementation)
        
        # Restore GPU state
        gpu.state.blend_set('NONE')
        gpu.state.depth_test_set('NONE')
        gpu.state.line_width_set(1.0)
        
    except Exception as e:
        print(f"Widget draw error: {e}")

def viewport_draw_handler():
    """Main draw handler for viewport widgets"""
    try:
        # Debug: Always print when handler is called
        # print("Widget draw handler called")

        # Only draw if we have an active item (don't require SDF viewport to be enabled)
        item, tree = get_active_sdf_item()
        if not item:
            # print("No active SDF item for widget drawing")
            return

        # print(f"Drawing widget for item: {item.name}")

        # Draw the simple widget
        draw_simple_widget()

    except Exception as e:
        print(f"SDF Widget Draw Handler Error: {e}")
        import traceback
        traceback.print_exc()

class SDF_OT_ToggleSimpleWidgets(bpy.types.Operator):
    """Toggle simple SDF viewport transform widgets"""
    bl_idname = "sdf.toggle_simple_widgets"
    bl_label = "Toggle Simple Widgets"
    bl_options = {'REGISTER'}

    def execute(self, context):
        print(f"Toggle widgets called - current state: {_widget_state['enabled']}")

        if _widget_state['enabled']:
            disable_simple_widgets()
            self.report({'INFO'}, "SDF simple widgets disabled")
        else:
            enable_simple_widgets()
            self.report({'INFO'}, "SDF simple widgets enabled")

        # Debug: Check if we have an active item
        item, tree = get_active_sdf_item()
        if item:
            print(f"Active SDF item: {item.name} at {item.get_world_location(tree)}")
        else:
            print("No active SDF item found")

        return {'FINISHED'}

class SDF_OT_DebugWidgets(bpy.types.Operator):
    """Debug widget system"""
    bl_idname = "sdf.debug_widgets"
    bl_label = "Debug Widgets"
    bl_options = {'REGISTER'}

    def execute(self, context):
        print("=== SDF Widget Debug ===")
        print(f"Widget enabled: {_widget_state['enabled']}")
        print(f"Draw handler: {_widget_state['draw_handler']}")

        # Check active item
        item, tree = get_active_sdf_item()
        if item:
            print(f"Active item: {item.name}")
            print(f"Item type: {item.item_type}")
            print(f"Local location: {tuple(item.location)}")
            print(f"World location: {item.get_world_location(tree)}")
        else:
            print("No active SDF item")

        # Check scene
        scene = context.scene
        if hasattr(scene, 'sdf_tree'):
            tree = scene.sdf_tree
            print(f"Tree items: {len(tree.items)}")
            print(f"Active index: {tree.active_index}")
        else:
            print("No SDF tree found")

        # Try to manually draw widget
        try:
            print("Attempting manual widget draw...")
            draw_simple_widget()
            print("Manual widget draw completed")
        except Exception as e:
            print(f"Manual widget draw failed: {e}")
            import traceback
            traceback.print_exc()

        return {'FINISHED'}

class SDF_OT_MoveItemInteractive(bpy.types.Operator):
    """Interactively move SDF item with mouse"""
    bl_idname = "sdf.move_item_interactive"
    bl_label = "Move Item Interactive"
    bl_options = {'REGISTER', 'UNDO'}
    
    axis: bpy.props.EnumProperty(
        items=[
            ('X', "X Axis", "Move along X axis"),
            ('Y', "Y Axis", "Move along Y axis"),
            ('Z', "Z Axis", "Move along Z axis"),
        ],
        default='X'
    )
    
    def modal(self, context, event):
        if event.type == 'MOUSEMOVE':
            # Get mouse delta
            delta_x = event.mouse_x - self.initial_mouse_x
            delta_y = event.mouse_y - self.initial_mouse_y
            
            # Calculate movement based on axis
            sensitivity = 0.01
            
            if self.axis == 'X':
                movement = delta_x * sensitivity
                self.item.location[0] = self.initial_location[0] + movement
            elif self.axis == 'Y':
                movement = delta_x * sensitivity
                self.item.location[1] = self.initial_location[1] + movement
            elif self.axis == 'Z':
                movement = delta_y * sensitivity
                self.item.location[2] = self.initial_location[2] + movement
            
            # Update viewport
            try:
                from .shaders import SDFRenderer
                SDFRenderer.refresh_shader()
            except:
                pass
            
            # Redraw viewport
            context.area.tag_redraw()
        
        elif event.type == 'LEFTMOUSE':
            return {'FINISHED'}
        elif event.type in {'RIGHTMOUSE', 'ESC'}:
            # Restore original location
            self.item.location = self.initial_location
            try:
                from .shaders import SDFRenderer
                SDFRenderer.refresh_shader()
            except:
                pass
            return {'CANCELLED'}
        
        return {'RUNNING_MODAL'}
    
    def invoke(self, context, event):
        # Get active item
        item, tree = get_active_sdf_item()
        if not item:
            self.report({'ERROR'}, "No active SDF item")
            return {'CANCELLED'}
        
        self.item = item
        self.initial_mouse_x = event.mouse_x
        self.initial_mouse_y = event.mouse_y
        self.initial_location = list(item.location)
        
        context.window_manager.modal_handler_add(self)
        return {'RUNNING_MODAL'}

def enable_simple_widgets():
    """Enable simple viewport widgets"""
    if _widget_state['enabled']:
        print("SDF simple widgets already enabled")
        return

    try:
        # Add draw handler
        _widget_state['draw_handler'] = bpy.types.SpaceView3D.draw_handler_add(
            viewport_draw_handler, (), 'WINDOW', 'POST_VIEW'
        )

        _widget_state['enabled'] = True
        print(f"✅ SDF simple widgets enabled - handler: {_widget_state['draw_handler']}")

        # Force a viewport redraw
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()

    except Exception as e:
        print(f"❌ Failed to enable SDF simple widgets: {e}")
        import traceback
        traceback.print_exc()

def disable_simple_widgets():
    """Disable simple viewport widgets"""
    if not _widget_state['enabled']:
        print("SDF simple widgets already disabled")
        return

    try:
        # Remove draw handler
        if _widget_state['draw_handler']:
            bpy.types.SpaceView3D.draw_handler_remove(
                _widget_state['draw_handler'], 'WINDOW'
            )
            print(f"✅ Removed draw handler: {_widget_state['draw_handler']}")
            _widget_state['draw_handler'] = None

        _widget_state['enabled'] = False
        print("✅ SDF simple widgets disabled")

        # Force a viewport redraw
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()

    except Exception as e:
        print(f"❌ Failed to disable SDF simple widgets: {e}")
        import traceback
        traceback.print_exc()

# Register classes
classes = [
    SDF_OT_ToggleSimpleWidgets,
    SDF_OT_DebugWidgets,
    SDF_OT_MoveItemInteractive,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    # Disable widgets first
    disable_simple_widgets()
    
    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except:
            pass
