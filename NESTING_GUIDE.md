# Primitive Nesting Guide for Arcane SDF

## Overview

The Arcane SDF addon now supports **primitive nesting**, allowing you to create complex parts by grouping primitives together in hierarchical structures. This enables you to:

- Create reusable compound shapes
- Apply transforms to groups of primitives
- Build complex geometries with organized structure
- Use boolean operations at multiple levels

## New Features

### 1. GROUP Item Type
- **Purpose**: Container for grouping multiple primitives
- **Icon**: Collection icon in the tree
- **Properties**: Location, Rotation, Scale (applied to all children)
- **Boolean Operations**: Groups can use Union, Subtract, or Intersect modes

### 2. Hierarchical Tree Structure
- **Visual Indentation**: Child items are visually indented in the tree
- **Parent-Child Relationships**: Items can be nested multiple levels deep
- **Recursive Processing**: GLSL generation handles nested structures automatically

### 3. New UI Controls

#### Grouping & Nesting Panel
Located below the primitive buttons, this panel provides:
- **Add Group**: Creates a new group container
- **Make Child Of...**: Moves selected item to be a child of another item
- **Add as Child**: Quick buttons to add primitives as children of the selected item

## How to Use

### Basic Grouping

1. **Create a Group**:
   - Click "Add Group" in the Grouping & Nesting panel
   - The group appears in the tree with a collection icon

2. **Add Children to Group**:
   - Select the group in the tree
   - Use "Add as Child" buttons to add primitives to the group
   - Or use the regular add buttons, then "Make Child Of..." to move items

3. **Configure Group Properties**:
   - Select the group to see its properties
   - Set Group Offset, Rotation, and Scale to transform all children
   - Set Boolean Mode to control how the group interacts with other items

### Advanced Nesting

#### Multi-Level Hierarchies
```
Root Group (Union)
├── Sphere (Union)
├── Sub Group (Subtract)
│   ├── Box (Union)
│   └── Cylinder (Union)
└── Torus (Union)
```

#### Transform Inheritance
- Group transforms affect all children recursively
- Child transforms are applied relative to their parent
- Nested groups can have their own transforms

### Example Workflows

#### Creating a Complex Part

1. **Base Shape**:
   ```
   Main Group
   └── Base Sphere (radius: 2.0)
   ```

2. **Add Details**:
   ```
   Main Group
   ├── Base Sphere (Union)
   └── Detail Group (Union)
       ├── Small Sphere 1
       ├── Small Sphere 2
       └── Small Sphere 3
   ```

3. **Add Cutouts**:
   ```
   Main Group
   ├── Base Sphere (Union)
   ├── Detail Group (Union)
   │   ├── Small Sphere 1
   │   ├── Small Sphere 2
   │   └── Small Sphere 3
   └── Cutout Group (Subtract)
       ├── Hole Cylinder 1
       └── Hole Cylinder 2
   ```

#### Reusable Components

1. Create a group with a complex shape
2. Duplicate the group for reuse
3. Modify group transforms to position copies
4. Use different boolean modes for variations

## Technical Details

### GLSL Generation
- **Hierarchical Processing**: Each level of nesting is processed recursively
- **Variable Management**: Unique variable names prevent conflicts
- **Transform Application**: Group transforms are applied to child coordinates
- **Boolean Operations**: Applied at each level of the hierarchy

### Performance Considerations
- **Nesting Depth**: Deeper nesting creates more complex GLSL
- **Group Count**: Many groups increase compilation time
- **Transform Complexity**: Complex rotations may impact performance

### Limitations
- **Rotation**: Currently only Z-axis rotation is fully implemented
- **Transform Order**: Transforms are applied in a fixed order (translate, rotate, scale)
- **GLSL Complexity**: Very deep nesting may hit shader compilation limits

## Tips and Best Practices

### Organization
- **Meaningful Names**: Give groups and items descriptive names
- **Logical Grouping**: Group related primitives together
- **Shallow Hierarchies**: Avoid excessive nesting depth

### Performance
- **Group Wisely**: Don't create unnecessary groups
- **Test Frequently**: Check viewport performance with complex hierarchies
- **Simplify When Possible**: Use fewer primitives when the result is similar

### Workflow
- **Plan Structure**: Think about the hierarchy before building
- **Build Incrementally**: Add complexity gradually
- **Use Boolean Modes**: Leverage Union, Subtract, and Intersect effectively

## Troubleshooting

### Common Issues

1. **Item Not Appearing as Child**:
   - Ensure the parent item is selected before adding children
   - Check that the parent index is set correctly

2. **Transforms Not Working**:
   - Verify group transforms are applied to the group, not individual children
   - Check that child transforms are relative to their parent

3. **GLSL Errors**:
   - Simplify the hierarchy if compilation fails
   - Check for circular parent-child relationships

4. **Performance Issues**:
   - Reduce nesting depth
   - Combine similar primitives when possible
   - Use fewer groups for simple shapes

### Debug Information
- Check the Blender console for error messages
- Use the "Update Viewport" button to refresh the shader
- Verify tree structure in the UI list

## Future Enhancements

Planned improvements include:
- Full 3D rotation support
- Transform constraints and limits
- Group templates and presets
- Import/export of hierarchical structures
- Visual hierarchy editing tools

---

**Note**: This nesting system is designed to be intuitive while providing powerful capabilities for creating complex SDF shapes. Start with simple hierarchies and gradually build more complex structures as you become familiar with the system.
