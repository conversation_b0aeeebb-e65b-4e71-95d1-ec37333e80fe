import bpy
from bpy.types import <PERSON><PERSON><PERSON>, UIList, Operator
from bpy.props import (
    StringProperty, 
    EnumProperty, 
    FloatProperty, 
    FloatVectorProperty,
    IntProperty,
    BoolProperty,
    CollectionProperty,
    PointerProperty
)

# Update callback for tree item properties
def _update_tree_item(self, context):
    """Update callback for tree item properties"""
    try:
        from .shaders import SDF<PERSON>enderer
        if SDFRenderer.is_enabled():
            SDFRenderer.refresh_shader()
    except Exception as e:
        print(f"SDF Tree: Error updating shader: {e}")

# SDF Tree Item Types
class SDFTreeItem(PropertyGroup):
    """Individual item in the SDF tree"""

    name: StringProperty(
        name="Name",
        default="SDF Item",
        update=_update_tree_item
    )

    item_type: EnumProperty(
        name="Type",
        items=[
            ('SPHERE', "Sphere", "Sphere primitive"),
            ('BOX', "Box", "Box primitive"),
            ('CYLINDER', "Cylinder", "Cylinder primitive"),
            ('TORUS', "Torus", "Torus primitive"),
            ('GROUP', "Group", "Group container for nesting primitives"),
            ('TRANSLATE', "Translate", "Translation transform"),
            ('ROTATE', "Rotate", "Rotation transform"),
            ('SCALE', "Scale", "Scale transform"),
        ],
        default='SPHERE',
        update=_update_tree_item
    )

    # Boolean operation mode for this primitive
    boolean_mode: EnumProperty(
        name="Boolean Mode",
        items=[
            ('UNION', "Union", "Add this shape to the result"),
            ('SUBTRACT', "Subtract", "Subtract this shape from the result"),
            ('INTERSECT', "Intersect", "Keep only intersection with previous shapes"),
        ],
        default='UNION',
        update=_update_tree_item
    )
    
    # Primitive properties
    radius: FloatProperty(name="Radius", default=1.0, min=0.0, update=_update_tree_item)
    size: FloatVectorProperty(name="Size", default=(1.0, 1.0, 1.0), size=3, update=_update_tree_item)
    height: FloatProperty(name="Height", default=2.0, min=0.0, update=_update_tree_item)
    major_radius: FloatProperty(name="Major Radius", default=1.0, min=0.0, update=_update_tree_item)
    minor_radius: FloatProperty(name="Minor Radius", default=0.3, min=0.0, update=_update_tree_item)

    # Transform properties
    location: FloatVectorProperty(name="Location", default=(0.0, 0.0, 0.0), size=3, subtype='TRANSLATION', update=_update_tree_item)
    rotation: FloatVectorProperty(name="Rotation", default=(0.0, 0.0, 0.0), size=3, subtype='EULER', update=_update_tree_item)
    scale: FloatVectorProperty(name="Scale", default=(1.0, 1.0, 1.0), size=3, update=_update_tree_item)

    # Operation properties
    smooth_radius: FloatProperty(name="Smooth Radius", default=0.0, min=0.0, update=_update_tree_item)

    # Edge modification properties
    bevel_radius: FloatProperty(
        name="Bevel Radius",
        description="Radius for rounding sharp edges",
        default=0.0,
        min=0.0,
        max=1.0,
        update=_update_tree_item
    )

    chamfer_size: FloatProperty(
        name="Chamfer Size",
        description="Size of chamfer cuts on edges (45-degree cuts)",
        default=0.0,
        min=0.0,
        max=1.0,
        update=_update_tree_item
    )

    fillet_radius: FloatProperty(
        name="Fillet Radius",
        description="Radius for rounding internal corners and concave edges",
        default=0.0,
        min=0.0,
        max=1.0,
        update=_update_tree_item
    )
    
    # Tree structure
    parent_index: IntProperty(default=-1)
    indent_level: IntProperty(default=0)
    is_expanded: BoolProperty(default=True)
    is_enabled: BoolProperty(default=True, update=_update_tree_item)
    
    def generate_primitive_glsl(self):
        """Generate GLSL code for just this primitive (no boolean operations)"""
        if self.item_type == 'SPHERE':
            # Spheres can have bevel (makes them smaller) but chamfer doesn't make sense
            base_sdf = f"length(p - vec3{tuple(self.location)}) - {self.radius}"
            if self.bevel_radius > 0.0:
                return f"({base_sdf}) - {self.bevel_radius}"
            return base_sdf

        elif self.item_type == 'BOX':
            loc = tuple(self.location)
            size = tuple(self.size)

            # Choose the appropriate box function based on edge modification
            # Priority: Bevel > Chamfer > Fillet > Normal
            if self.bevel_radius > 0.0:
                return f"sdBoxBeveled(p - vec3{loc}, vec3{size}, {self.bevel_radius})"
            elif self.chamfer_size > 0.0:
                return f"sdBoxChamfered(p - vec3{loc}, vec3{size}, {self.chamfer_size})"
            elif self.fillet_radius > 0.0:
                return f"sdBoxFilleted(p - vec3{loc}, vec3{size}, {self.fillet_radius})"
            else:
                return f"sdBox(p - vec3{loc}, vec3{size})"

        elif self.item_type == 'CYLINDER':
            loc = tuple(self.location)

            # Choose the appropriate cylinder function based on edge modification
            # Priority: Bevel > Chamfer > Fillet > Normal
            if self.bevel_radius > 0.0:
                return f"sdCylinderBeveled(p - vec3{loc}, {self.radius}, {self.height * 0.5}, {self.bevel_radius})"
            elif self.chamfer_size > 0.0:
                return f"sdCylinderChamfered(p - vec3{loc}, {self.radius}, {self.height * 0.5}, {self.chamfer_size})"
            elif self.fillet_radius > 0.0:
                return f"sdCylinderFilleted(p - vec3{loc}, {self.radius}, {self.height * 0.5}, {self.fillet_radius})"
            else:
                return f"sdCylinder(p - vec3{loc}, {self.radius}, {self.height * 0.5})"

        elif self.item_type == 'TORUS':
            loc = tuple(self.location)
            base_sdf = f"sdTorus(p - vec3{loc}, {self.major_radius}, {self.minor_radius})"
            # Torus can have bevel but chamfer is complex
            if self.bevel_radius > 0.0:
                return f"({base_sdf}) - {self.bevel_radius}"
            return base_sdf

        elif self.item_type == 'TRANSLATE':
            offset = tuple(self.location)
            return f"sdf(p - vec3{offset})"

        elif self.item_type == 'GROUP':
            # Groups don't generate primitive GLSL directly
            # They are handled in the hierarchical generation
            return "1000.0"  # Default large distance for empty groups

        return "1000.0"  # Default large distance

# UI List for SDF Tree
class SDF_UL_TreeList(UIList):
    """UI List for SDF tree items"""
    
    def draw_item(self, context, layout, data, item, icon, active_data, active_propname, index):
        if self.layout_type in {'DEFAULT', 'COMPACT'}:
            # Indentation for hierarchy
            for i in range(item.indent_level):
                layout.label(text="", icon='BLANK1')
            
            # No expand/collapse needed for new system
            layout.label(text="", icon='BLANK1')
            
            # Enable/disable toggle
            layout.prop(item, "is_enabled", text="", icon='HIDE_OFF' if item.is_enabled else 'HIDE_ON')
            
            # Item icon and name
            primitive_icons = {
                'SPHERE': 'MESH_UVSPHERE',
                'BOX': 'MESH_CUBE',
                'CYLINDER': 'MESH_CYLINDER',
                'TORUS': 'MESH_TORUS',
                'GROUP': 'OUTLINER_COLLECTION',
                'TRANSLATE': 'ORIENTATION_GLOBAL',
                'ROTATE': 'DRIVER_ROTATIONAL_DIFFERENCE',
                'SCALE': 'FULLSCREEN_EXIT'
            }

            # Show boolean mode icon for primitives
            boolean_icons = {
                'UNION': 'SELECT_EXTEND',
                'SUBTRACT': 'SELECT_SUBTRACT',
                'INTERSECT': 'SELECT_INTERSECT',
            }

            # Use primitive icon as main icon
            main_icon = primitive_icons.get(item.item_type, 'OBJECT_DATA')
            layout.prop(item, "name", text="", emboss=False, icon=main_icon)

            # Show boolean mode as a small indicator
            if item.item_type in ['SPHERE', 'BOX', 'CYLINDER', 'TORUS', 'GROUP']:
                bool_icon = boolean_icons.get(item.boolean_mode, 'SELECT_EXTEND')
                layout.label(text="", icon=bool_icon)
        
        elif self.layout_type in {'GRID'}:
            layout.alignment = 'CENTER'
            layout.label(text="", icon='OBJECT_DATA')

# SDF Tree Collection
class SDFTreeCollection(PropertyGroup):
    """Collection of SDF tree items"""
    
    items: CollectionProperty(type=SDFTreeItem)
    active_index: IntProperty(default=0)
    
    def add_item(self, item_type, name=None, parent_index=-1):
        """Add a new item to the tree"""
        item = self.items.add()
        item.item_type = item_type
        item.name = name or item_type.title()
        item.parent_index = parent_index

        # Calculate indent level
        if parent_index >= 0 and parent_index < len(self.items) - 1:
            parent = self.items[parent_index]
            item.indent_level = parent.indent_level + 1
        else:
            item.indent_level = 0

        # Trigger shader update
        self._update_shader()

        return item
    
    def remove_item(self, index):
        """Remove an item and its children"""
        if 0 <= index < len(self.items):
            # Remove children first
            self._remove_children(index)
            # Remove the item itself
            self.items.remove(index)
            # Update active index
            if self.active_index >= len(self.items):
                self.active_index = max(0, len(self.items) - 1)

            # Trigger shader update
            self._update_shader()
    
    def _remove_children(self, parent_index):
        """Recursively remove children of an item"""
        i = len(self.items) - 1
        while i >= 0:
            if i < len(self.items) and self.items[i].parent_index == parent_index:
                self._remove_children(i)
                self.items.remove(i)
            i -= 1
    
    def move_item(self, from_index, to_index):
        """Move an item in the tree"""
        if 0 <= from_index < len(self.items) and 0 <= to_index < len(self.items):
            self.items.move(from_index, to_index)
            # Trigger shader update
            self._update_shader()

    def get_children(self, parent_index):
        """Get all direct children of a parent item"""
        return [i for i, item in enumerate(self.items) if item.parent_index == parent_index]

    def get_all_descendants(self, parent_index):
        """Get all descendants (children, grandchildren, etc.) of a parent item"""
        descendants = []
        children = self.get_children(parent_index)
        for child_index in children:
            descendants.append(child_index)
            descendants.extend(self.get_all_descendants(child_index))
        return descendants

    def set_parent(self, item_index, new_parent_index):
        """Set the parent of an item and update indent levels"""
        if 0 <= item_index < len(self.items):
            item = self.items[item_index]
            item.parent_index = new_parent_index

            # Update indent level
            if new_parent_index >= 0 and new_parent_index < len(self.items):
                parent = self.items[new_parent_index]
                item.indent_level = parent.indent_level + 1
            else:
                item.indent_level = 0

            # Update indent levels for all descendants
            self._update_descendant_indent_levels(item_index)

            # Trigger shader update
            self._update_shader()

    def _update_descendant_indent_levels(self, parent_index):
        """Recursively update indent levels for all descendants"""
        if parent_index >= 0 and parent_index < len(self.items):
            parent_indent = self.items[parent_index].indent_level
            children = self.get_children(parent_index)
            for child_index in children:
                self.items[child_index].indent_level = parent_indent + 1
                self._update_descendant_indent_levels(child_index)

    def _update_shader(self):
        """Update the SDF shader"""
        try:
            from .shaders import SDFRenderer
            if SDFRenderer.is_enabled():
                SDFRenderer.refresh_shader()
        except Exception as e:
            print(f"SDF Tree: Error updating shader: {e}")
    
    def ensure_default_item(self):
        """Ensure the tree has at least one item"""
        if not self.items:
            print("SDF Tree: Adding default sphere to empty tree")
            self.add_item('SPHERE', name="Default Sphere")
            self.active_index = 0

    def generate_glsl(self):
        """Generate GLSL code for the entire tree using boolean modes"""
        # Ensure we have at least one item
        self.ensure_default_item()

        if not self.items:
            return "return 1000.0;"  # No geometry

        # TEMPORARY: Revert to processing ALL enabled items like the original code
        # This should restore the working behavior
        enabled_items = [item for item in self.items if item.is_enabled]

        if not enabled_items:
            return "return 1000.0;"

        if len(enabled_items) == 1:
            # Single item - just return its primitive
            glsl = enabled_items[0].generate_primitive_glsl()
            return f"return {glsl};"

        # Multiple items - combine them using their boolean modes
        glsl_parts = []

        # First item is always the base (union mode)
        first_item = enabled_items[0]
        glsl_parts.append(f"float result = {first_item.generate_primitive_glsl()};")

        # Process remaining items according to their boolean mode
        for i, item in enumerate(enabled_items[1:], 1):
            primitive_glsl = item.generate_primitive_glsl()
            glsl_parts.append(f"float d{i} = {primitive_glsl};")

            if item.boolean_mode == 'UNION':
                if item.smooth_radius > 0.0:
                    glsl_parts.append(f"result = smin(result, d{i}, {item.smooth_radius});")
                else:
                    glsl_parts.append(f"result = min(result, d{i});")

            elif item.boolean_mode == 'SUBTRACT':
                if item.smooth_radius > 0.0:
                    glsl_parts.append(f"result = ssubtract(result, d{i}, {item.smooth_radius});")
                else:
                    glsl_parts.append(f"result = max(result, -d{i});")

            elif item.boolean_mode == 'INTERSECT':
                if item.smooth_radius > 0.0:
                    glsl_parts.append(f"result = smax(result, d{i}, {item.smooth_radius});")
                else:
                    glsl_parts.append(f"result = max(result, d{i});")

        # Return the final result
        glsl_parts.append("return result;")
        return "\n".join(glsl_parts)





# Register classes
classes = [
    SDFTreeItem,
    SDF_UL_TreeList,
    SDFTreeCollection,
]

@bpy.app.handlers.persistent
def initialize_tree_on_load(dummy):
    """Initialize SDF tree when file loads"""
    try:
        for scene in bpy.data.scenes:
            if hasattr(scene, 'sdf_tree'):
                # Tree property exists, ensure it's accessible
                tree = scene.sdf_tree

                # If tree is empty, add a default sphere
                if not tree.items:
                    print(f"SDF Tree: Adding default sphere to scene {scene.name}")
                    tree.add_item('SPHERE', name="Default Sphere")
                    tree.active_index = 0

                print(f"SDF Tree initialized for scene: {scene.name} with {len(tree.items)} items")
    except Exception as e:
        print(f"Error initializing SDF tree: {e}")

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

    # Add load handler
    if initialize_tree_on_load not in bpy.app.handlers.load_post:
        bpy.app.handlers.load_post.append(initialize_tree_on_load)

def unregister():
    # Remove load handler
    if initialize_tree_on_load in bpy.app.handlers.load_post:
        bpy.app.handlers.load_post.remove(initialize_tree_on_load)

    for cls in reversed(classes):
        try:
            bpy.utils.unregister_class(cls)
        except:
            pass
